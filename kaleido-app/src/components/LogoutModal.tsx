import React from 'react';

import { logoutUser } from '@/lib/apiHelper';
import { useRouter } from 'next/navigation';

interface LogoutModalProps {
  isOpen?: boolean;
  onClose: () => void;
  onLogout?: () => void;
  onValidate?: () => void;
  onContinue?: () => void;
  message?: string;
  title?: string;
  autoLogout?: boolean;
}

const LogoutModal: React.FC<LogoutModalProps> = ({
  isOpen = true,
  onClose,
  onLogout,
  onValidate,
  onContinue,
  message = 'We detected a problem with your profile data. Please sign out and sign back in to refresh your session.',
  title = 'Session Issue Detected',
  autoLogout = false,
}) => {
  const router = useRouter();

  // Default logout handler if none provided
  const handleLogout = () => {
    if (onLogout) {
      onLogout();
      return;
    }

    // Use the centralized logout function with federated logout
    // This ensures complete session clearing
    logoutUser({ federated: true, returnTo: '/' });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[9999] flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 m-4">
        <div className="text-center mb-4">
          <div className="h-16 w-16 mx-auto mb-4 flex items-center justify-center rounded-full bg-amber-100">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8 text-amber-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          <p className="mt-2 text-sm text-gray-600">{message}</p>
        </div>
        <div className="flex flex-col sm:flex-row sm:justify-center gap-3 mt-5">
          {onContinue && (
            <button
              onClick={onContinue}
              className="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              Continue
            </button>
          )}
          {onValidate && (
            <button
              onClick={onValidate}
              className="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Validate Profile
            </button>
          )}
          <button
            onClick={handleLogout}
            className="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
          >
            Sign Out
          </button>
          <button
            onClick={onClose}
            className="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Skip
          </button>
        </div>
      </div>
    </div>
  );
};

export default LogoutModal;
