'use client';

import { EmptyState } from '@/components/ui/EmptyState';
import apiHelper from '@/lib/apiHelper';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  Award,
  BarChart3,
  Calendar,
  CheckCircle2,
  ChevronRight,
  Clock,
  GitCompare,
  Loader2,
  Sparkles,
  Target,
  Users,
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { ComparisonDetailModal } from './comparisons/ComparisonDetailModal';

interface ComparisonsTabProps {
  candidateId: string;
  jobId: string;
}

interface Comparison {
  id: string;
  comparisonTitle: string;
  comparisonType: string;
  status: 'pending' | 'completed' | 'failed';
  createdAt: string;
  candidateIds: string[];
  comparisonResults?: {
    summary: string;
    executiveSummary?: string;
    detailedComparison?: {
      [candidateId: string]: any;
    };
    candidateAnalysis?: {
      [candidateId: string]: any;
    };
    recommendations?: any;
    tradeOffs?: Array<{
      candidateA: string;
      candidateB: string;
      comparison: string;
    }>;
    headToHeadComparisons?: Array<{
      candidate1: string;
      candidate2: string;
      keyDifference: string;
      recommendation: string;
    }>;
    criticalConsiderations?: string[];
  };
  visualizationData?: any;
  metadata?: any;
}

export const ComparisonsTab: React.FC<ComparisonsTabProps> = ({ candidateId, jobId }) => {
  const [selectedComparison, setSelectedComparison] = useState<Comparison | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  // Fetch comparisons for this job
  const {
    data: comparisons,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['comparisons', jobId],
    queryFn: async () => {
      const response = await apiHelper.get<Comparison[]>(`/comparisons/jobs/${jobId}`);
      return response;
    },
    staleTime: 1000, // 1 second - reduced to ensure fresh data
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  // Add useEffect to refetch on mount
  useEffect(() => {
    refetch();
  }, [refetch]);

  // Debug logging
  useEffect(() => {
    console.log('ComparisonsTab Debug:', {
      candidateId,
      jobId,
      comparisons,
      totalComparisons: comparisons?.length,
    });
  }, [candidateId, jobId, comparisons]);

  // Use all comparisons for this job (not filtered by candidate)
  const jobComparisons = comparisons || [];

  // Check which comparisons include the current candidate
  const comparisonsWithCandidate = jobComparisons.filter(comp =>
    comp.candidateIds.includes(candidateId)
  );

  // Get the latest comparison
  const latestComparison = jobComparisons[0];

  // Calculate stats
  const totalComparisons = jobComparisons.length;
  const completedComparisons = jobComparisons.filter(c => c.status === 'completed').length;
  const avgCandidatesPerComparison =
    jobComparisons.length > 0
      ? Math.round(
          jobComparisons.reduce((acc, c) => acc + c.candidateIds.length, 0) / jobComparisons.length
        )
      : 0;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="w-8 h-8 animate-spin text-purple-600" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-500">Failed to load comparisons</p>
      </div>
    );
  }

  if (jobComparisons.length === 0) {
    return (
      <EmptyState
        type="generic"
        icon={GitCompare}
        title="No Comparisons Yet"
        description="No comparisons have been created for this job yet. Select multiple candidates to create a comparison."
        showButton={false}
      />
    );
  }

  const handleViewComparison = (comparison: Comparison) => {
    setSelectedComparison(comparison);
    setShowDetailModal(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 'pending':
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 'failed':
        return 'text-red-400 bg-red-400/10 border-red-400/20';
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  return (
    <div className="space-y-6">
      {/* Hero Section with Header Image */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="relative h-48 rounded-xl overflow-hidden"
      >
        {/* Background image with gradient overlay */}
        <div
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: `url('/images/landing/exclusive-2.webp')`,
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-r from-purple-900/90 via-purple-800/70 to-transparent" />
        <div className="absolute inset-0 bg-gradient-to-t from-slate-900 via-transparent to-transparent" />

        {/* Content over image */}
        <div className="relative z-10 h-full flex items-center justify-between p-6">
          <div className="flex-1">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <h2 className="text-3xl lg:text-4xl font-bold text-white mb-2">
                Comparison Analysis
              </h2>
              <p className="text-white/80 text-lg">Track and review all candidate comparisons</p>
            </motion.div>
          </div>

          {/* Enhanced Stats positioned at bottom right */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="absolute bottom-4 right-6"
          >
            <div className="flex items-center gap-4">
              {/* Total Comparisons */}
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-lg bg-blue-500/20 flex items-center justify-center border border-blue-400/30 shadow-lg">
                  <GitCompare className="w-4 h-4 lg:w-5 lg:h-5 text-blue-400" />
                </div>
                <div className="flex flex-col">
                  <span className="text-base lg:text-lg font-bold text-white">
                    {totalComparisons}
                  </span>
                  <span className="text-[10px] lg:text-xs text-white/60 font-medium">Total</span>
                </div>
              </div>

              <div className="w-px h-6 lg:h-8 bg-white/20" />

              {/* Completed */}
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-lg bg-green-500/20 flex items-center justify-center border border-green-400/30 shadow-lg">
                  <CheckCircle2 className="w-4 h-4 lg:w-5 lg:h-5 text-green-400" />
                </div>
                <div className="flex flex-col">
                  <span className="text-base lg:text-lg font-bold text-white">
                    {completedComparisons}
                  </span>
                  <span className="text-[10px] lg:text-xs text-white/60 font-medium">
                    Completed
                  </span>
                </div>
              </div>

              <div className="w-px h-6 lg:h-8 bg-white/20" />

              {/* Average Candidates */}
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-lg bg-purple-500/20 flex items-center justify-center border border-purple-400/30 shadow-lg">
                  <Users className="w-4 h-4 lg:w-5 lg:h-5 text-purple-400" />
                </div>
                <div className="flex flex-col">
                  <span className="text-base lg:text-lg font-bold text-white">
                    {avgCandidatesPerComparison}
                  </span>
                  <span className="text-[10px] lg:text-xs text-white/60 font-medium">Avg Size</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Latest Comparison Card */}
      {latestComparison && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="relative bg-gradient-to-br from-purple-600/10 to-pink-600/5 backdrop-blur-sm rounded-xl border border-purple-400/20 p-6 overflow-hidden cursor-pointer hover:border-purple-400/40 transition-all"
          onClick={() => handleViewComparison(latestComparison)}
        >
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-purple-500/10 rounded-full blur-3xl" />

          <div className="relative z-10">
            <div className="flex items-start justify-between mb-4">
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-600 to-pink-600 flex items-center justify-center">
                    <Award className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">Latest Comparison</h3>
                    <p className="text-xs text-white/60">Most recent analysis</p>
                  </div>
                </div>
                <h4 className="text-base font-medium text-white/90 mb-1">
                  {latestComparison.comparisonTitle}
                </h4>
                {latestComparison.candidateIds.includes(candidateId) && (
                  <span className="inline-flex items-center px-2.5 py-1 text-xs font-medium bg-green-500/20 text-green-400 rounded-full border border-green-400/30 mt-2">
                    <CheckCircle2 className="w-3 h-3 mr-1" />
                    You're included in this comparison
                  </span>
                )}
              </div>

              <span
                className={`px-2.5 py-1 rounded-full text-xs font-medium border ${getStatusColor(latestComparison.status)}`}
              >
                {latestComparison.status}
              </span>
            </div>

            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1.5 text-white/70">
                <Users className="w-4 h-4 text-purple-400" />
                <span>{latestComparison.candidateIds.length} candidates</span>
              </div>
              <div className="flex items-center gap-1.5 text-white/70">
                <Calendar className="w-4 h-4 text-blue-400" />
                <span>
                  {new Date(latestComparison.createdAt).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric',
                  })}
                </span>
              </div>
            </div>

            {latestComparison.comparisonResults?.executiveSummary && (
              <p className="mt-3 text-sm text-white/70 line-clamp-2">
                {latestComparison.comparisonResults.executiveSummary}
              </p>
            )}

            {/* Show recommendation if available */}
            {latestComparison.comparisonResults?.recommendations?.topChoice && (
              <div className="mt-4 p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/5 rounded-lg border border-green-400/20">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center flex-shrink-0">
                    <Target className="w-4 h-4 text-green-400" />
                  </div>
                  <div>
                    <p className="text-xs font-medium text-green-400 mb-1">Top Recommendation</p>
                    <p className="text-sm text-white/80">
                      {latestComparison.comparisonResults.recommendations.topChoice.candidateId}
                    </p>
                    <p className="text-xs text-white/60 mt-1 line-clamp-2">
                      {latestComparison.comparisonResults.recommendations.topChoice.reasoning}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Show key insights */}
            {latestComparison.comparisonResults?.candidateAnalysis && (
              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-3">
                {(() => {
                  // Find this candidate's analysis
                  let candidateAnalysis = null;
                  let candidateName = null;

                  // First try direct ID match
                  if (latestComparison.comparisonResults.candidateAnalysis[candidateId]) {
                    candidateAnalysis =
                      latestComparison.comparisonResults.candidateAnalysis[candidateId];
                  } else {
                    // Try to find by name from metadata
                    const candidateMetadata = latestComparison.metadata?.candidateNames?.find(
                      c => c.id === candidateId
                    );
                    if (candidateMetadata?.name) {
                      candidateName = candidateMetadata.name;
                      candidateAnalysis =
                        latestComparison.comparisonResults.candidateAnalysis[candidateName];
                    }
                  }

                  if (!candidateAnalysis) return null;

                  return (
                    <>
                      <div className="p-3 bg-purple-500/10 rounded-lg border border-purple-400/20">
                        <div className="flex items-center gap-2 mb-2">
                          <Sparkles className="w-4 h-4 text-purple-400" />
                          <p className="text-xs font-medium text-purple-400">Your Ranking</p>
                        </div>
                        <div className="flex items-baseline gap-2">
                          <span className="text-2xl font-bold text-white">
                            #{candidateAnalysis.overallRank || 'N/A'}
                          </span>
                          <span className="text-xs text-white/60">
                            out of {latestComparison.candidateIds.length} candidates
                          </span>
                        </div>
                        {candidateAnalysis.keyDifferentiator && (
                          <p className="mt-2 text-xs text-white/70 line-clamp-2">
                            {candidateAnalysis.keyDifferentiator}
                          </p>
                        )}
                      </div>

                      {/* Show scores if available */}
                      {candidateAnalysis.scores && (
                        <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-400/20">
                          <div className="flex items-center gap-2 mb-2">
                            <BarChart3 className="w-4 h-4 text-blue-400" />
                            <p className="text-xs font-medium text-blue-400">Score Breakdown</p>
                          </div>
                          <div className="grid grid-cols-2 gap-2">
                            {Object.entries(candidateAnalysis.scores)
                              .slice(0, 4)
                              .map(([skill, score]: [string, any]) => (
                                <div key={skill} className="flex items-center justify-between">
                                  <span className="text-xs text-white/60 capitalize">
                                    {skill.replace(/([A-Z])/g, ' $1').trim()}
                                  </span>
                                  <span className="text-xs font-medium text-white">{score}%</span>
                                </div>
                              ))}
                          </div>
                        </div>
                      )}
                    </>
                  );
                })()}
              </div>
            )}

            <div className="mt-4 flex items-center gap-2 text-sm font-medium text-purple-400">
              <span>View Full Analysis</span>
              <ChevronRight className="w-4 h-4" />
            </div>
          </div>
        </motion.div>
      )}

      {/* Comparison History */}
      {jobComparisons.length > 1 && (
        <div className="space-y-3">
          <h3 className="text-base font-semibold text-white flex items-center gap-2">
            <Clock className="w-4 h-4 text-purple-400" />
            Previous Comparisons
          </h3>

          <div className="bg-white/[0.02] backdrop-blur-sm rounded-lg border border-white/5 overflow-hidden">
            {jobComparisons.slice(1).map((comparison, index) => (
              <motion.div
                key={comparison.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="px-5 py-4 hover:bg-white/[0.03] transition-all cursor-pointer border-b border-white/[0.03] last:border-b-0"
                onClick={() => handleViewComparison(comparison)}
              >
                <div className="flex items-center justify-between">
                  {/* Title Column */}
                  <div className="flex-1 min-w-0 pr-4">
                    <div className="flex items-center gap-2">
                      <GitCompare className="w-4 h-4 text-purple-400/70" />
                      <h4 className="text-sm font-medium text-white/90 truncate">
                        {comparison.comparisonTitle}
                      </h4>
                      {comparison.candidateIds.includes(candidateId) && (
                        <span className="px-2 py-0.5 text-xs font-medium bg-green-500/20 text-green-400 rounded-full border border-green-400/30">
                          Includes You
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Status Column */}
                  <div className="flex items-center justify-center px-4">
                    <span
                      className={`px-2.5 py-1 rounded-full text-xs font-medium border ${getStatusColor(comparison.status)}`}
                    >
                      {comparison.status}
                    </span>
                  </div>

                  {/* Candidates Column */}
                  <div className="flex items-center gap-1.5 px-4 min-w-[120px]">
                    <Users className="w-3.5 h-3.5 text-purple-400" />
                    <span className="text-xs text-purple-300 font-medium">
                      {comparison.candidateIds.length} candidates
                    </span>
                  </div>

                  {/* Date Column */}
                  <div className="flex items-center gap-1.5 px-4 min-w-[100px]">
                    <Calendar className="w-3.5 h-3.5 text-blue-400" />
                    <span className="text-xs text-blue-300 font-medium">
                      {new Date(comparison.createdAt).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                      })}
                    </span>
                  </div>

                  {/* Action Column */}
                  <div className="pl-4">
                    <ChevronRight className="w-4 h-4 text-white/30 group-hover:text-white/50 transition-colors" />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Detail Modal */}
      {selectedComparison && (
        <ComparisonDetailModal
          isOpen={showDetailModal}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedComparison(null);
          }}
          comparison={selectedComparison}
        />
      )}
    </div>
  );
};
