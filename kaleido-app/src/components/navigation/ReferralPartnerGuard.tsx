'use client';

import { useEffect, useState } from 'react';
import { referralApi } from '@/app/referral-partner/services/referralApi';
import { ReferralPartner } from '@/app/referral-partner/types';
import { EmptyState } from '@/components/ui/EmptyState';
import { HeartHandshake } from 'lucide-react';
import { showToast } from '@/components/Toaster';

interface ReferralPartnerGuardProps {
  children: React.ReactNode;
}

interface ReferralPartnerStatus {
  exists: boolean;
  partner?: ReferralPartner;
}

export function ReferralPartnerGuard({ children }: ReferralPartnerGuardProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [partnerStatus, setPartnerStatus] = useState<ReferralPartnerStatus | null>(null);
  const [isActivating, setIsActivating] = useState(false);

  useEffect(() => {
    checkPartnerStatus();
  }, []);

  const checkPartnerStatus = async () => {
    try {
      setIsLoading(true);
      const status = await referralApi.checkReferralStatus();
      setPartnerStatus({
        exists: status.isActive,
        partner: status.partner,
      });
    } catch (error) {
      console.error('Error checking referral partner status:', error);
      setPartnerStatus({ exists: false });
    } finally {
      setIsLoading(false);
    }
  };

  const handleActivateReferral = async () => {
    try {
      setIsActivating(true);

      // Try to activate for job seeker first, fallback to company activation
      let partner: ReferralPartner;
      try {
        partner = await referralApi.activateForJobSeeker();
      } catch (error) {
        // If job seeker activation fails, try company activation
        partner = await referralApi.activateForCompany();
      }

      setPartnerStatus({ exists: true, partner });

      showToast({
        message: 'Referral partner account activated successfully!',
        type: 'success',
      });
    } catch (error) {
      console.error('Error activating referral partner:', error);
      showToast({
        message: 'Failed to activate referral partner account. Please try again.',
        type: 'error',
      });
    } finally {
      setIsActivating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  // If partner doesn't exist, show activation screen
  if (!partnerStatus?.exists) {
    return (
      <EmptyState
        title="Activate Referral Program"
        description="Join our referral partner program to earn commissions by referring qualified candidates to open positions."
        icon={HeartHandshake}
        actionLabel={isActivating ? 'Activating...' : 'Activate Referral Partner'}
        onAction={handleActivateReferral}
        showButton={!isActivating}
        type="generic"
      />
    );
  }

  // If partner exists, render the children (navigation items)
  return <>{children}</>;
}
