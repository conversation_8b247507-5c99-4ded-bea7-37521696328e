'use client';

import React, { Suspense, useEffect, useRef, useState } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import AccessDenied from '@/components/AccessDenied';
import { HelpWidget } from '@/components/help';
import IntercomSupport from '@/components/IntercomSupport';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import StatusCompletionModalManager from '@/components/shared/StatusCompletionModalManager';
import useUser from '@/hooks/useUser';
import useEnhancedUserData from '@/hooks/useEnhancedUserData';
import { useRole } from '@/hooks/useRole';
import { apiClient as apiHelper, logoutUser } from '@/lib/apiHelper';
import { UserRole } from '@/types/roles';

import AdminLayout from './AdminLayout';
import EmployerLayout from './EmployerLayout';
import GraduateLayout from './GraduateLayout';
import JobSeekerLayout from './JobSeekerLayout';
import ReferralPartnerLayout from './ReferralPartnerLayout';

// Add a loader context to share loading state between layouts
export const LoaderContext = React.createContext({
  setApiLoading: (_isLoading: boolean) => {},
  isApiLoading: false,
});

// Define reducer actions and state for loader management
type LoaderAction =
  | { type: 'SET_LOADING'; isLoading: boolean }
  | { type: 'SHOW_LOADER' }
  | { type: 'HIDE_LOADER' };

type LoaderState = {
  isApiLoading: boolean;
  showLoader: boolean;
};

function loaderReducer(state: LoaderState, action: LoaderAction): LoaderState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isApiLoading: action.isLoading };
    case 'SHOW_LOADER':
      return { ...state, showLoader: true };
    case 'HIDE_LOADER':
      return { ...state, showLoader: false };
    default:
      return state;
  }
}

interface AppLayoutProps {
  children: React.ReactNode;
  isLoading?: boolean;
}

// Main AppLayout component that doesn't directly use useSearchParams
const AppLayout: React.FC<AppLayoutProps> = ({ children, isLoading = false }) => {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <ColorfulSmokeyOrbLoader />
        </div>
      }
    >
      <AppLayoutContent isLoading={isLoading}>{children}</AppLayoutContent>
    </Suspense>
  );
};

// Inner component that safely uses useSearchParams
const AppLayoutContent: React.FC<AppLayoutProps> = ({ children, isLoading = false }) => {
  const { user, isLoading: userLoading } = useUser();
  const searchParams = useSearchParams() ?? new URLSearchParams();
  const router = useRouter();

  // Use the new role hook to get role from JWT claims
  const { role: userRole, loading: roleLoading } = useRole();

  // Use the enhanced user data hook to get authoritative role from backend
  const {
    userData,
    isLoading: userDataLoading,
    error: userDataError,
    isFallback,
  } = useEnhancedUserData();

  const [validationError, setValidationError] = useState<string | null>(null);
  const [showLogoutModal, setShowLogoutModal] = useState(false);

  // Reference for content div (no scaling)
  const contentRef = useRef<HTMLDivElement>(null);

  // Use a ref for tracking the timeout to avoid dependency issues
  const loaderTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Use reducer for loader state management
  const [loaderState, dispatchLoader] = React.useReducer(loaderReducer, {
    isApiLoading: false,
    showLoader: false,
  });

  // Function to manage API loading state
  const handleApiLoadingChange = React.useCallback((isLoading: boolean) => {
    // First dispatch the loading state change
    dispatchLoader({ type: 'SET_LOADING', isLoading });

    // Clear any existing timeout
    if (loaderTimeoutRef.current) {
      clearTimeout(loaderTimeoutRef.current);
      loaderTimeoutRef.current = null;
    }

    if (isLoading) {
      // Set a timeout to show loader after 5 seconds
      loaderTimeoutRef.current = setTimeout(() => {
        dispatchLoader({ type: 'SHOW_LOADER' });
      }, 5000);
    } else {
      // Immediately hide the loader
      dispatchLoader({ type: 'HIDE_LOADER' });
    }
  }, []);

  // Create a stable context value that won't cause infinite renders
  const loaderContextValue = React.useMemo(
    () => ({
      setApiLoading: handleApiLoadingChange,
      isApiLoading: loaderState.isApiLoading,
    }),
    [handleApiLoadingChange, loaderState.isApiLoading]
  );

  // Removed scaling calculation effect

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (loaderTimeoutRef.current) {
        clearTimeout(loaderTimeoutRef.current);
      }
    };
  }, []);

  // Get role from URL if present (for initial role setting only)
  const roleFromUrl = searchParams.get('role');

  // Handle role from URL parameter only (pendingUserRole is now handled by PostLoginRoleHandler)
  useEffect(() => {
    const handleInitialRole = async () => {
      if (!user?.sub) return;

      // Only handle role from URL parameter, not from localStorage
      // PostLoginRoleHandler now handles pendingUserRole from localStorage
      const initialRole = roleFromUrl;

      if (!initialRole) return;

      // Wait for auth session to be ready before making API calls
      const authSessionReady = sessionStorage.getItem('auth_session_ready') === 'true';
      if (!authSessionReady) {
        return;
      }

      try {
        // Update role in database
        await apiHelper.put(`/roles/${user.sub}`, { role: initialRole });

        // Update local storage for middleware compatibility
        const roleData = { role: initialRole, clientId: user.sub };
        localStorage.setItem(`userRole_${user.sub}`, JSON.stringify(roleData));

        // Also set cookie for middleware access
        const expires = new Date();
        expires.setTime(expires.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days
        document.cookie = `userRole_${user.sub}=${encodeURIComponent(JSON.stringify(roleData))};expires=${expires.toUTCString()};path=/;SameSite=Lax;Secure`;

        // Clean up URL parameter (pendingUserRole is handled by PostLoginRoleHandler)
        if (roleFromUrl) {
          const newParams = new URLSearchParams(searchParams);
          newParams.delete('role');
          router.replace(
            newParams.toString() ? `?${newParams.toString()}` : window.location.pathname
          );
        }
      } catch (error) {
        console.error('Error updating user role:', error);
      }
    };

    handleInitialRole();
  }, [user, roleFromUrl, router, searchParams]);

  // Handle user data errors
  useEffect(() => {
    if (userDataError) {
      console.error('Error fetching user data:', userDataError);
      setValidationError('Unable to load user data. Some features may be limited.');
    }
  }, [userDataError]);

  // This function is now defined later in the component

  // Initialize Intercom when the user is loaded and set login timestamp
  useEffect(() => {
    // This will be handled by the IntercomProvider
    // Just ensuring that the component is mounted

    // Set login timestamp if not already set
    if (user?.sub && !localStorage.getItem(`last_login_${user.sub}`)) {
      localStorage.setItem(`last_login_${user.sub}`, Date.now().toString());
    }
  }, [user]);

  // Optimize layout transitions
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // After user is loaded and before rendering the layout
  useEffect(() => {
    setMounted(true);

    // Sync all roles from localStorage to cookies for middleware access
    if (typeof window !== 'undefined') {
      try {
        // Find all userRole_ keys in localStorage and sync to cookies
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith('userRole_')) {
            const roleData = localStorage.getItem(key);
            if (roleData) {
              // Set cookie with 30 days expiry
              const expires = new Date();
              expires.setTime(expires.getTime() + 30 * 24 * 60 * 60 * 1000);
              document.cookie = `${key}=${encodeURIComponent(roleData)};expires=${expires.toUTCString()};path=/;SameSite=Lax;Secure`;
            }
          }
        }
      } catch (error) {
        console.error('Error syncing roles to cookies:', error);
      }
    }

    // Check for pending uploads when component mounts
    if (typeof window !== 'undefined' && user?.sub) {
      const pendingUploads = JSON.parse(localStorage.getItem('pendingUploads') || '{}');

      // Clean up any stale uploads (older than 12 hours)
      const now = new Date();
      let hasChanges = false;

      Object.entries(pendingUploads).forEach(([key, upload]: [string, any]) => {
        const uploadTime = new Date(upload.timestamp);
        const hoursDiff = (now.getTime() - uploadTime.getTime()) / (1000 * 60 * 60);

        if (hoursDiff > 12) {
          delete pendingUploads[key];
          hasChanges = true;
        }
      });

      if (hasChanges) {
        localStorage.setItem('pendingUploads', JSON.stringify(pendingUploads));
      }
    }
  }, [user]);

  // Add timeout for role loading to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (roleLoading && !userLoading) {
        console.warn('AppLayout: Role loading timeout - continuing without JWT role');
      }
    }, 5000);

    return () => clearTimeout(timeout);
  }, [roleLoading, userLoading]);

  // Prevent layout shift during hydration
  if (!mounted) {
    return null;
  }

  // If still loading user info, show loading state
  // Don't wait for role loading if user is loaded (fallback to localStorage)
  if (userLoading || !user?.sub) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <ColorfulSmokeyOrbLoader />
      </div>
    );
  }

  // Get the effective role (from JWT claims or backend)
  let effectiveRole = userRole || userData?.userRole;

  // Fallback: Try to get role from localStorage if not found in JWT or backend
  if (!effectiveRole && user?.sub) {
    try {
      const storedRoleData = localStorage.getItem(`userRole_${user.sub}`);
      if (storedRoleData) {
        const parsed = JSON.parse(storedRoleData);
        if (parsed.role && Object.values(UserRole).includes(parsed.role)) {
          effectiveRole = parsed.role as UserRole;
        }
      }
    } catch (e) {
      // Ignore parse errors
    }
  }

  // Safety check: if no valid role is found, show error instead of redirecting
  if (!effectiveRole || !Object.values(UserRole).includes(effectiveRole)) {
    // If data is still loading or Auth0 session is not ready, show loader
    const authSessionReady = sessionStorage.getItem('auth_session_ready') === 'true';
    if (userDataLoading || roleLoading || !authSessionReady) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <ColorfulSmokeyOrbLoader />
        </div>
      );
    }

    // Only log error once when data is fully loaded
    console.error('AppLayout: No valid role found for user', {
      userId: user?.sub,
      userDataRole: userData?.userRole,
      effectiveRole,
      userDataLoading: false,
      roleLoading: false,
    });

    // Check if we have a role in URL or localStorage as temporary fallback
    const urlRole = searchParams.get('role');
    if (urlRole && Object.values(UserRole).includes(urlRole as UserRole)) {
      // Continue with URL role temporarily
      const effectiveRole = urlRole as UserRole;

      return (
        <LoaderContext.Provider value={loaderContextValue}>
          <div ref={contentRef} className="relative min-h-screen overflow-auto">
            {effectiveRole === UserRole.ADMIN && <AdminLayout>{children}</AdminLayout>}
            {effectiveRole === UserRole.EMPLOYER && <EmployerLayout>{children}</EmployerLayout>}
            {effectiveRole === UserRole.JOB_SEEKER && <JobSeekerLayout>{children}</JobSeekerLayout>}
            {effectiveRole === UserRole.GRADUATE && <GraduateLayout>{children}</GraduateLayout>}
            {effectiveRole === UserRole.REFERRAL_PARTNER && (
              <ReferralPartnerLayout>{children}</ReferralPartnerLayout>
            )}
            <IntercomSupport />
            <HelpWidget />
            <StatusCompletionModalManager />
          </div>
        </LoaderContext.Provider>
      );
    }

    // Check if we've been waiting too long (grace period of 10 seconds after login)
    const lastLoginTime = localStorage.getItem(`last_login_${user?.sub}`);
    const gracePeriodMs = 10000; // 10 seconds grace period
    const isWithinGracePeriod =
      lastLoginTime && Date.now() - parseInt(lastLoginTime) < gracePeriodMs;

    if (isWithinGracePeriod) {
      // Still within grace period, show loader
      return (
        <div className="min-h-screen flex items-center justify-center">
          <ColorfulSmokeyOrbLoader />
        </div>
      );
    }

    // Clear all cache and redirect to signout
    console.log('No valid role found after grace period, clearing cache and signing out user');

    // Use centralized logout with federated option
    logoutUser({ federated: true, returnTo: window.location.origin });

    // Show loading state while redirecting
    return (
      <div className="min-h-screen flex items-center justify-center">
        <ColorfulSmokeyOrbLoader />
      </div>
    );
  }

  // Handle logout
  const handleLogout = () => {
    // Use centralized logout with federated option
    logoutUser({ federated: true });
  };

  // Handle continue anyway
  const handleContinueAnyway = () => {
    setShowLogoutModal(false);
    if (user?.sub) {
      localStorage.setItem(`skip_validation_${user.sub}`, 'true');
      localStorage.setItem(`last_login_${user.sub}`, Date.now().toString());
    }
  };

  return (
    <LoaderContext.Provider value={loaderContextValue}>
      {/* {showLogoutModal && (
        <LogoutModal
          onClose={handleContinueAnyway}
          onLogout={handleLogout}
          message={
            validationError ||
            'There was an issue with your session. Would you like to log out and log back in?'
          }
          title="Session Issue Detected"
        />
      )} */}

      {validationError && !showLogoutModal ? (
        <AccessDenied
          message={validationError}
          redirectUrl="/dashboard"
          redirectLabel="Go to Dashboard"
        />
      ) : (
        <div ref={contentRef} className="relative min-h-screen overflow-auto">
          {isLoading ? (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-blue-800 to-purple-800">
              <ColorfulSmokeyOrbLoader />
            </div>
          ) : (
            <>
              {/* Warning banner when using fallback data */}
              {isFallback && (
                <div className="bg-yellow-600/90 backdrop-blur-sm text-white px-4 py-2 text-sm text-center border-b border-yellow-500/50">
                  <span className="font-medium">⚠️ Limited connectivity:</span> Some features may be
                  unavailable. Data shown is from local cache.
                </div>
              )}

              {/* Content layout based on authoritative user role from backend */}
              {effectiveRole === UserRole.ADMIN && <AdminLayout>{children}</AdminLayout>}
              {effectiveRole === UserRole.EMPLOYER && <EmployerLayout>{children}</EmployerLayout>}
              {effectiveRole === UserRole.JOB_SEEKER && (
                <JobSeekerLayout>{children}</JobSeekerLayout>
              )}
              {effectiveRole === UserRole.GRADUATE && <GraduateLayout>{children}</GraduateLayout>}
              {effectiveRole === UserRole.REFERRAL_PARTNER && (
                <ReferralPartnerLayout>{children}</ReferralPartnerLayout>
              )}

              {/* Support widget */}
              <IntercomSupport />

              {/* Help widget */}
              <HelpWidget />

              {/* Status Completion Modal Manager - listens for events from background services */}
              <StatusCompletionModalManager />
            </>
          )}
        </div>
      )}
    </LoaderContext.Provider>
  );
};

export default AppLayout;
