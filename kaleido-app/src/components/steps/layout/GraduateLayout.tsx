'use client';

import React, { useContext, useEffect, useState } from 'react';

import { GraduateSetupSlider } from '@/components/Graduate/GraduateSetupSlider';
import { useAuth0Token } from '@/hooks/useAuth0Token';
import { useCachedApi } from '@/hooks/useCachedApi';
import apiHelper, { logoutUser } from '@/lib/apiHelper';
import { UserRole } from '@/types/roles';
import { LoaderContext } from './AppLayout';
import BaseLayout from './BaseLayout';

interface ValidationResponse {
  isValid: boolean;
  missingFields: string[];
  hasCompletedOnboarding: boolean;
  completion: {
    overall: number;
    sections: {
      basicInfo: number;
      professionalInfo: number;
      preferences: number;
      additionalInfo: number;
    };
  };
  record?: any; // Replace with proper Graduate profile type if available
}

interface GraduateLayoutProps {
  children: React.ReactNode;
  isLoading?: boolean;
}

const GraduateLayout: React.FC<GraduateLayoutProps> = ({ children, isLoading = false }) => {
  const { user, isLoading: userLoading } = useAuth0Token();
  const [showGraduateSetup, setShowGraduateSetup] = useState(false);
  const [graduateInitialData, setGraduateInitialData] = useState({});
  const [isChecking, setIsChecking] = useState(true);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [isValidationNeeded, setIsValidationNeeded] = useState(true);
  const { setApiLoading } = useContext(LoaderContext);
  const { getCached } = useCachedApi();

  // Add function to handle unauthorized error
  const handleUnauthorized = async () => {
    // Use centralized logout with federated option
    await logoutUser({ federated: true });
  };

  // Update the effect with the new config
  useEffect(() => {
    if (!user) return;

    // Check for existing validation status in localStorage
    const skipValidation = localStorage.getItem(`skip_validation_${user.sub}`);
    if (skipValidation === 'true') {
      // User has already chosen to skip validation, continue loading
      setIsValidationNeeded(false);
      setIsChecking(false);
    } else {
      // Check if this is a new user by looking for profile existence
      const checkUserStatus = async () => {
        try {
          // Try to validate the user's profile to determine if they're new
          const validation = await apiHelper.post(`/graduates/validate-profile`, user);

          // If user has completed onboarding, they're an existing user
          if (validation && validation.hasCompletedOnboarding) {
            // This is an existing user - check if validation is needed
            const lastLogin = localStorage.getItem(`last_login_${user.sub}`);
            const currentTime = Date.now();
            const validationInterval = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

            if (!lastLogin || currentTime - parseInt(lastLogin) > validationInterval) {
              // This is a fresh session for an existing user, show the validation modal
              setShowLogoutModal(true);
            } else {
              // Not a fresh session, just proceed without validation
              setIsValidationNeeded(false);
              setIsChecking(false);
              // Still mark validation as skipped to avoid future checks
              localStorage.setItem(`skip_validation_${user.sub}`, 'true');
            }

            // Update the last login time
            localStorage.setItem(`last_login_${user.sub}`, currentTime.toString());
          } else {
            // This is a new user or user hasn't completed onboarding
            // Skip validation modal and let them go through normal onboarding
            setIsValidationNeeded(false);
            setIsChecking(false);
            setShowLogoutModal(false);
            // Mark as skipped so they don't see the modal later
            localStorage.setItem(`skip_validation_${user.sub}`, 'true');
            localStorage.setItem(`last_login_${user.sub}`, Date.now().toString());
          }
        } catch (error) {
          // If there's an error fetching profile (e.g., 404 for new user), treat as new user
          setIsValidationNeeded(false);
          setIsChecking(false);
          setShowLogoutModal(false);
          // Mark as skipped so they don't see the modal later
          localStorage.setItem(`skip_validation_${user.sub}`, 'true');
          localStorage.setItem(`last_login_${user.sub}`, Date.now().toString());
        }
      };

      checkUserStatus();
    }
  }, [user]);

  // Function to run validation when user chooses to continue
  const runProfileValidation = async () => {
    if (!user) return;
    setShowLogoutModal(false);

    // Set up an abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      setApiLoading(true);

      // Use our cached API hook with abort signal
      const validation = await apiHelper.post(`/graduates/validate-profile`, user, {
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      // Clear the timeout since request completed
      clearTimeout(timeoutId);

      setShowGraduateSetup(!validation.isValid);

      if (validation.record) {
        setGraduateInitialData({
          ...validation.record,
          firstName: validation.record.firstName || user.name,
          lastName: validation.record.lastName || '',
          email: validation.record.email || user.email,
        });
      }

      // Save to localStorage that validation was successful
      localStorage.setItem(`skip_validation_${user.sub}`, 'true');
    } catch (error: any) {
      // Clear the timeout
      clearTimeout(timeoutId);

      console.error('Error checking graduate profile:', error);

      // Handle AbortError (timeout)
      if (error.name === 'AbortError') {
        console.warn('Profile validation request timed out');
      }

      // Check if this is a redirect error (403 handled by apiHelper)
      if (error.redirectPerformed) {
        return;
      }

      // Handle unauthorized (403) errors by logging out (fallback for any 403s not handled by apiHelper)
      if (error?.response?.status === 403) {
        await handleUnauthorized();
        return;
      }

      // For all errors, show setup slider as fallback
      setShowGraduateSetup(true);
    } finally {
      setIsChecking(false);
      setApiLoading(false);
    }
  };

  // Function to handle modal continue button - skip validation
  const handleContinueAnyway = () => {
    // Store the skip validation preference in localStorage
    if (user?.sub) {
      localStorage.setItem(`skip_validation_${user.sub}`, 'true');
      // Also update last login time
      localStorage.setItem(`last_login_${user.sub}`, Date.now().toString());
    }

    setShowLogoutModal(false);
    setIsValidationNeeded(false);
    setIsChecking(false);
  };

  const handleGraduateSetup = async (data: any) => {
    try {
      setApiLoading(true);
      setShowGraduateSetup(false);
    } catch (error) {
      console.error('Error saving graduate profile:', error);
    } finally {
      setApiLoading(false);
    }
  };

  return (
    <>
      <BaseLayout userRole={UserRole.GRADUATE} isLoading={isLoading || isChecking}>
        {children}
      </BaseLayout>

      {/* Logout Modal */}
      {/* <LogoutModal
        isOpen={showLogoutModal && !userLoading && !!user}
        onClose={handleContinueAnyway}
        onLogout={handleUnauthorized}
        onValidate={runProfileValidation}
        message="We need to validate your profile data to ensure you have access to all features. Would you like to run validation now, skip it, or sign out and sign back in?"
      /> */}

      {/* Graduate setup slider */}
      {showGraduateSetup && (
        <GraduateSetupSlider
          onComplete={handleGraduateSetup}
          onClose={() => setShowGraduateSetup(false)}
          initialData={graduateInitialData}
        />
      )}
    </>
  );
};

export default GraduateLayout;
