import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useRouter, useSearchParams } from 'next/navigation';
import AppLayout from '../AppLayout';
import useUser from '@/hooks/useUser';
import { useRole } from '@/hooks/useRole';
import useEnhancedUserData from '@/hooks/useEnhancedUserData';
import { UserRole } from '@/types/roles';
import { logoutUser } from '@/lib/apiHelper';

// Mock dependencies
const mockPush = jest.fn();
const mockReplace = jest.fn();

jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: mockPush,
    replace: mockReplace,
  })),
  useSearchParams: jest.fn(() => new URLSearchParams()),
}));
jest.mock('@/hooks/useUser');
jest.mock('@/hooks/useRole');
jest.mock('@/hooks/useEnhancedUserData');
jest.mock('@/lib/apiHelper', () => ({
  apiClient: {
    put: jest.fn(),
  },
  logoutUser: jest.fn(),
}));

// Mock child components
jest.mock('../AdminLayout', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="admin-layout">{children}</div>
  ),
}));

jest.mock('../EmployerLayout', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="employer-layout">{children}</div>
  ),
}));

jest.mock('../JobSeekerLayout', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="jobseeker-layout">{children}</div>
  ),
}));

jest.mock('../GraduateLayout', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="graduate-layout">{children}</div>
  ),
}));

jest.mock('../ReferralPartnerLayout', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="referral-layout">{children}</div>
  ),
}));

jest.mock('@/components/Layouts/ColourfulLoader', () => ({
  __esModule: true,
  default: () => <div data-testid="loader">Loading...</div>,
}));

jest.mock('@/components/IntercomSupport', () => ({
  __esModule: true,
  default: () => <div data-testid="intercom">Intercom</div>,
}));

jest.mock('@/components/help', () => ({
  HelpWidget: () => <div data-testid="help-widget">Help</div>,
}));

jest.mock('@/components/shared/StatusCompletionModalManager', () => ({
  __esModule: true,
  default: () => <div data-testid="status-modal">Status Modal</div>,
}));

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;
const mockUseRole = useRole as jest.MockedFunction<typeof useRole>;
const mockUseEnhancedUserData = useEnhancedUserData as jest.MockedFunction<
  typeof useEnhancedUserData
>;
const mockLogoutUser = logoutUser as jest.MockedFunction<typeof logoutUser>;

describe('AppLayout Authentication Flow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    sessionStorage.clear();

    // Reset mocks
    mockPush.mockClear();
    mockReplace.mockClear();
    if (mockLogoutUser) {
      mockLogoutUser.mockClear();
    }

    // Reset search params
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams());

    // Set auth session as ready by default
    sessionStorage.setItem('auth_session_ready', 'true');
  });

  describe('Loading States', () => {
    it('should show loader when user is loading', () => {
      mockUseUser.mockReturnValue({
        user: null,
        isLoading: true,
      });
      mockUseRole.mockReturnValue({
        role: null,
        loading: true,
      });
      mockUseEnhancedUserData.mockReturnValue({
        userData: null,
        isLoading: true,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      });

      render(
        <AppLayout>
          <div>Test Content</div>
        </AppLayout>
      );

      expect(screen.getByTestId('loader')).toBeInTheDocument();
      expect(screen.queryByText('Test Content')).not.toBeInTheDocument();
    });

    it('should show loader when auth session is not ready', () => {
      sessionStorage.clear(); // Remove auth_session_ready

      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123' },
        isLoading: false,
      });
      mockUseRole.mockReturnValue({
        role: null,
        loading: false,
      });
      mockUseEnhancedUserData.mockReturnValue({
        userData: null,
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      });

      render(
        <AppLayout>
          <div>Test Content</div>
        </AppLayout>
      );

      expect(screen.getByTestId('loader')).toBeInTheDocument();
    });
  });

  describe('Role Detection', () => {
    it('should render JobSeekerLayout when role is JOB_SEEKER from JWT', () => {
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123' },
        isLoading: false,
      });
      mockUseRole.mockReturnValue({
        role: UserRole.JOB_SEEKER,
        loading: false,
      });
      mockUseEnhancedUserData.mockReturnValue({
        userData: null,
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      });

      render(
        <AppLayout>
          <div>Test Content</div>
        </AppLayout>
      );

      expect(screen.getByTestId('jobseeker-layout')).toBeInTheDocument();
      expect(screen.getByText('Test Content')).toBeInTheDocument();
    });

    it('should render layout based on userData role when JWT role is not available', () => {
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123' },
        isLoading: false,
      });
      mockUseRole.mockReturnValue({
        role: null,
        loading: false,
      });
      mockUseEnhancedUserData.mockReturnValue({
        userData: {
          userRole: UserRole.EMPLOYER,
          dashboardStats: {} as any,
        },
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      });

      render(
        <AppLayout>
          <div>Test Content</div>
        </AppLayout>
      );

      expect(screen.getByTestId('employer-layout')).toBeInTheDocument();
    });

    it('should fallback to localStorage role when no role in JWT or userData', () => {
      const userId = 'auth0|123';
      localStorage.setItem(
        `userRole_${userId}`,
        JSON.stringify({ role: UserRole.GRADUATE, clientId: userId })
      );

      mockUseUser.mockReturnValue({
        user: { sub: userId },
        isLoading: false,
      });
      mockUseRole.mockReturnValue({
        role: null,
        loading: false,
      });
      mockUseEnhancedUserData.mockReturnValue({
        userData: null,
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      });

      render(
        <AppLayout>
          <div>Test Content</div>
        </AppLayout>
      );

      expect(screen.getByTestId('graduate-layout')).toBeInTheDocument();
    });

    it('should use URL parameter role as temporary fallback', () => {
      const searchParams = new URLSearchParams({ role: UserRole.ADMIN });
      (useSearchParams as jest.Mock).mockReturnValue(searchParams);

      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123' },
        isLoading: false,
      });
      mockUseRole.mockReturnValue({
        role: null,
        loading: false,
      });
      mockUseEnhancedUserData.mockReturnValue({
        userData: null,
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      });

      // Mock console methods for this test
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      render(
        <AppLayout>
          <div>Test Content</div>
        </AppLayout>
      );

      expect(screen.getByTestId('admin-layout')).toBeInTheDocument();

      // Restore console methods
      consoleErrorSpy.mockRestore();
      consoleLogSpy.mockRestore();
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      // Mock console.error to avoid test output noise
      jest.spyOn(console, 'error').mockImplementation(() => {});
    });

    afterEach(() => {
      (console.error as jest.Mock).mockRestore();
    });

    it('should log error only once when no valid role is found', async () => {
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123' },
        isLoading: false,
      });
      mockUseRole.mockReturnValue({
        role: null,
        loading: false,
      });
      mockUseEnhancedUserData.mockReturnValue({
        userData: null,
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      });

      // Set last login to avoid grace period
      localStorage.setItem(`last_login_auth0|123`, '1');

      render(
        <AppLayout>
          <div>Test Content</div>
        </AppLayout>
      );

      await waitFor(() => {
        expect(console.error).toHaveBeenCalled();
        expect(console.error).toHaveBeenCalledWith(
          'AppLayout: No valid role found for user',
          expect.objectContaining({
            userId: 'auth0|123',
            userDataRole: undefined,
            effectiveRole: undefined,
            userDataLoading: false,
            roleLoading: false,
          })
        );
      });
    });

    it('should not log error while data is still loading', () => {
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123' },
        isLoading: false,
      });
      mockUseRole.mockReturnValue({
        role: null,
        loading: true, // Still loading
      });
      mockUseEnhancedUserData.mockReturnValue({
        userData: null,
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      });

      render(
        <AppLayout>
          <div>Test Content</div>
        </AppLayout>
      );

      expect(console.error).not.toHaveBeenCalled();
    });

    it('should show loader during grace period after login', () => {
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123' },
        isLoading: false,
      });
      mockUseRole.mockReturnValue({
        role: null,
        loading: false,
      });
      mockUseEnhancedUserData.mockReturnValue({
        userData: null,
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      });

      // Set recent login time (within grace period)
      localStorage.setItem(`last_login_auth0|123`, Date.now().toString());

      render(
        <AppLayout>
          <div>Test Content</div>
        </AppLayout>
      );

      // During grace period, should show loader
      expect(screen.getByTestId('loader')).toBeInTheDocument();

      // The error might still be logged but that's okay as long as loader is shown
      // The important thing is that the user sees the loader, not an error
    });

    it('should redirect to logout when no role found after grace period', async () => {
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123' },
        isLoading: false,
      });
      mockUseRole.mockReturnValue({
        role: null,
        loading: false,
      });
      mockUseEnhancedUserData.mockReturnValue({
        userData: null,
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      });

      // Set old login time (outside grace period)
      localStorage.setItem(`last_login_auth0|123`, '1');

      render(
        <AppLayout>
          <div>Test Content</div>
        </AppLayout>
      );

      await waitFor(() => {
        // Check that logoutUser was called with correct parameters
        expect(mockLogoutUser).toHaveBeenCalledWith({
          federated: true,
          returnTo: expect.any(String),
        });
      });
    });
  });

  describe('Fallback Handling', () => {
    it('should show warning banner when using fallback data', () => {
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123' },
        isLoading: false,
      });
      mockUseRole.mockReturnValue({
        role: UserRole.JOB_SEEKER,
        loading: false,
      });
      mockUseEnhancedUserData.mockReturnValue({
        userData: {
          userRole: UserRole.JOB_SEEKER,
          dashboardStats: {} as any,
        },
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: true, // Using fallback data
      });

      render(
        <AppLayout>
          <div>Test Content</div>
        </AppLayout>
      );

      expect(screen.getByText(/Limited connectivity/)).toBeInTheDocument();
      expect(screen.getByText(/Some features may be unavailable/)).toBeInTheDocument();
    });
  });

  describe('Multiple Role Sources Priority', () => {
    it('should prioritize roles in correct order: JWT > userData > localStorage > URL', () => {
      const userId = 'auth0|123';

      // Set all possible role sources
      localStorage.setItem(
        `userRole_${userId}`,
        JSON.stringify({ role: UserRole.GRADUATE, clientId: userId })
      );

      const searchParams = new URLSearchParams({ role: UserRole.ADMIN });
      (useSearchParams as jest.Mock).mockReturnValue(searchParams);

      mockUseUser.mockReturnValue({
        user: { sub: userId },
        isLoading: false,
      });

      // JWT role takes priority
      mockUseRole.mockReturnValue({
        role: UserRole.JOB_SEEKER,
        loading: false,
      });

      mockUseEnhancedUserData.mockReturnValue({
        userData: {
          userRole: UserRole.EMPLOYER,
          dashboardStats: {} as any,
        },
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      });

      render(
        <AppLayout>
          <div>Test Content</div>
        </AppLayout>
      );

      // Should use JWT role (JOB_SEEKER)
      expect(screen.getByTestId('jobseeker-layout')).toBeInTheDocument();
    });
  });
});
