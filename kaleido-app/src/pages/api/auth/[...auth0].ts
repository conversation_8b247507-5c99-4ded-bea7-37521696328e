import { handleAuth, handleCallback, handleLogin, Session } from '@auth0/nextjs-auth0';

interface LinkedInData {
  provider?: string;
  profile?: any;
}

import { handleLogout } from '@auth0/nextjs-auth0';

export default handleAuth({
  signup: handleLogin({
    authorizationParams: {
      screen_hint: 'signup',
      audience: process.env.AUTH0_AUDIENCE,
      scope:
        'openid profile email offline_access r_emailaddress r_liteprofile r_basicprofile r_fullprofile',
      // Force interactive signup - prevent automatic session pickup
      prompt: 'login',
      // Ensure fresh authentication
      max_age: 0,
    },
  }),
  logout: handleLogout({
    returnTo: '/',
    logoutParams: {
      returnTo: process.env.AUTH0_BASE_URL
        ? `${process.env.AUTH0_BASE_URL}/`
        : 'http://localhost:3000/',
      // Force federated logout to clear Auth0 session completely
      federated: true,
      // Add client_id to ensure proper logout
      client_id: process.env.AUTH0_CLIENT_ID,
    },
  }),
  login: handleLogin({
    returnTo: '/dashboard',
    authorizationParams: {
      audience: process.env.AUTH0_AUDIENCE,
      scope:
        'openid profile email offline_access r_emailaddress r_liteprofile r_basicprofile r_fullprofile',
      // Force interactive login - prevent automatic session pickup
      prompt: 'login',
      // Ensure fresh authentication
      max_age: 0,
    },
    getLoginState(req: any, _loginOptions: any) {
      // Extract role and mode from query parameters
      const role = req.query?.role;
      const mode = req.query?.mode;
      const isSignup = req.query?.screen_hint === 'signup';
      const defaultReturnTo = req.query?.returnTo || '/dashboard';
      const loginHint = req.query?.login_hint;

      // Determine returnTo based on role and signup status
      let finalReturnTo = defaultReturnTo as string;

      // For new signups, always redirect to dashboard
      // The dashboard will check if onboarding is needed and redirect accordingly
      // This prevents showing onboarding screens before checking if the user already has data
      if (isSignup && defaultReturnTo === '/dashboard') {
        finalReturnTo = '/dashboard';
      }

      // Always include role in the returnTo URL as a query parameter
      if (role) {
        const separator = finalReturnTo.includes('?') ? '&' : '?';
        finalReturnTo = `${finalReturnTo}${separator}role=${role}`;
      }

      // Prepare authorization params with role metadata
      const authorizationParams: any = {};

      // If we have a login hint (from our role management), pass it through
      if (loginHint) {
        authorizationParams.login_hint = loginHint;
      }

      // If role is specified, also add it to user_metadata for new signups
      if (role && isSignup) {
        authorizationParams.user_metadata = JSON.stringify({
          role,
          signupSource: 'kaleido-app',
          signupTimestamp: new Date().toISOString(),
        });
      }

      return {
        returnTo: finalReturnTo,
        ...(role && { role: role as string }),
        ...(mode && { mode: mode as string }),
        ...(isSignup && { isSignup: true }),
        authorizationParams,
      };
    },
  }),
  callback: handleCallback({
    afterCallback: async (req: any, res: any, session: Session | null) => {
      // Clear any stale auth cookies first to ensure fresh session
      const cookiesToClear = [
        'appSession',
        'appSession.0',
        'appSession.1',
        'appSession.2',
        'auth_session',
        'a0.sso',
        '_auth0',
        'auth0_compat',
      ];

      // Clear old session cookies before setting new ones
      const clearCookieHeaders: string[] = [];
      cookiesToClear.forEach(cookieName => {
        clearCookieHeaders.push(`${cookieName}=; Max-Age=0; Path=/; HttpOnly=false; SameSite=Lax`);
      });

      // Also clear any role cookies from previous sessions
      if (req.cookies) {
        Object.keys(req.cookies).forEach(key => {
          if (key.startsWith('pendingRole_') || key.startsWith('userRole_')) {
            clearCookieHeaders.push(`${key}=; Max-Age=0; Path=/; HttpOnly=false; SameSite=Lax`);
          }
        });
      }

      // Apply cookie clearing
      if (clearCookieHeaders.length > 0) {
        res.setHeader('Set-Cookie', clearCookieHeaders);
      }

      // Ensure we have a session
      if (!session) {
        console.error('Auth0 callback: No session available after callback');
        throw new Error('No session available after callback');
      }

      // Extract role from request query or user metadata
      try {
        let role = null;

        // Check request query for role parameter
        const queryRole = req.query?.role as string;
        if (queryRole) {
          role = queryRole;
        }

        // Check if role is already in JWT claims from Auth0 Action
        const namespace = 'https://kaleidotalent.com';
        if (!role && session.user[`${namespace}/role`]) {
          role = session.user[`${namespace}/role`];
        }

        if (!role && session.user['role']) {
          role = session.user['role'];
        }

        // If we have a role, ensure it's available for the backend
        if (role && session.user) {
          // Add role to session user object
          session.user.role = role;
          session.user.pendingRole = role;

          // Store role in a cookie that the backend can access during JWT validation
          const roleData = {
            role: role,
            userId: session.user.sub,
            timestamp: Date.now(),
          };

          // Set a temporary cookie that expires in 5 minutes (enough time for user creation)
          const expires = new Date();
          expires.setTime(expires.getTime() + 5 * 60 * 1000); // 5 minutes
          res.setHeader(
            'Set-Cookie',
            `pendingRole_${session.user.sub}=${encodeURIComponent(JSON.stringify(roleData))};expires=${expires.toUTCString()};path=/;SameSite=Lax;HttpOnly=false`
          );
        }
      } catch (error) {
        console.warn('Auth0 callback: Could not extract role:', error);
        // Continue without role - backend will use localStorage sync
      }

      // Extract LinkedIn profile data if available
      const linkedInData: LinkedInData = {};

      // Check if this is a LinkedIn login by looking at identities
      if (session.user?.sub && session.user.sub.includes('linkedin')) {
        // Store LinkedIn connection info
        linkedInData.provider = 'linkedin';

        // Extract profile data from user_metadata or app_metadata if available
        if (session.user.user_metadata?.linkedin) {
          linkedInData.profile = session.user.user_metadata.linkedin;
        }

        // Try to extract more LinkedIn profile information
        // Access token information might be in id_token claims or accessToken payload
        const idTokenPayload = session.user?.__raw
          ? JSON.parse(Buffer.from(session.user.__raw.split('.')[1], 'base64').toString())
          : null;

        // Extract standard profile information
        linkedInData.profile = {
          ...linkedInData.profile,
          name: session.user.name,
          email: session.user.email,
          picture: session.user.picture,
          firstName: session.user.given_name || session.user.name?.split(' ')[0] || '',
          lastName:
            session.user.family_name || session.user.name?.split(' ').slice(1).join(' ') || '',
          profileUrl: session.user.profileUrl || idTokenPayload?.profileUrl,
          skills: idTokenPayload?.skills || session.user?.skills || [],
          headline: session.user.headline || idTokenPayload?.headline || '',
          industry: session.user.industry || idTokenPayload?.industry || '',
          summary: session.user.summary || idTokenPayload?.summary || '',
          positions: session.user.positions || idTokenPayload?.positions || [],
          location: session.user.locale || idTokenPayload?.locale || '',
          educations: session.user.educations || idTokenPayload?.educations || [],
        };

        // Log the extracted LinkedIn data for debugging

        // Try to extract additional data from identities if available
        if (session.user.identities && session.user.identities.length > 0) {
          const linkedInIdentity = session.user.identities.find(
            (identity: any) => identity.provider === 'linkedin'
          );

          if (linkedInIdentity && linkedInIdentity.profileData) {
            linkedInData.profile = {
              ...linkedInData.profile,
              ...linkedInIdentity.profileData,
              firstName: linkedInIdentity.profileData.firstName || linkedInData.profile.firstName,
              lastName: linkedInIdentity.profileData.lastName || linkedInData.profile.lastName,
              email: linkedInIdentity.profileData.email || linkedInData.profile.email,
            };
          }
        }

        // Store the LinkedIn data in the user object
        session.user = {
          ...session.user,
          linkedInProfile: linkedInData,
        };
      }

      // Make sure we have the access token in the user object
      if (session.accessToken) {
        session.user = {
          ...session.user,
          accessToken: session.accessToken,
        };

        // Add script to store session in localStorage
        res.setHeader(
          'Set-Cookie',
          'auth_session=' +
            JSON.stringify({
              accessToken: session.accessToken,
              expiresAt: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days expiry to match backend JWT and Auth0 session
            })
        );
      }

      return session;
    },
  }),
});
